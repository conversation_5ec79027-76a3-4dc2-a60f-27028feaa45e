"""配置选择GUI界面模块。

此模块提供了配置文件选择和管理的图形用户界面，包括：
- 配置文件列表显示
- 配置文件创建和加载
- 配置状态管理
- 主界面启动
"""
import asyncio
import logging
import os
import sys
from pathlib import Path
from tkinter import filedialog, messagebox
from typing import Dict, List, Optional
from zoneinfo import ZoneInfo

# 设置Python路径 - 必须在导入项目模块之前
if getattr(sys, 'frozen', False):
    # 打包后的环境
    src_path = str(Path(sys._MEIPASS))
else:
    # 开发环境，从当前文件位置推断
    src_path = str(Path(__file__).parent.parent)

# 确保源代码路径在sys.path中
if src_path not in sys.path:
    sys.path.insert(0, src_path)

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.constants import common_constants
from common.utils import path_manager  # 确保路径设置
from webhook_server.utils import webhook_server_utils
import webhook_server.webhook_server_gui as webhook_server_gui
from webhook_server.config import config_check, constants, gui_constants
from webhook_server.models import webhook_server
from common.models import gui_widgets, single_instance_meta
from webhook_server.utils import config_lock
from common.utils import server_utils, gui_utils, ttkb_gui_utils

logger: Optional[logging.Logger] = None


class ConfigSelectionGUI(metaclass=single_instance_meta.SingleInstanceMeta):
    """配置选择GUI界面类。

    提供配置文件选择和管理的图形用户界面，支持配置文件的创建、加载、
    状态管理等功能。使用单例模式确保只有一个实例。
    """

    def __init__(self) -> None:
        """初始化配置选择GUI界面。"""
        server_utils.logger_print(
            msg="initializing config selection gui", custom_logger=logger
        )
        # 配置管理器 参数值是gui界面生成配置文件的配置项的值,两者统一
        self.config_manager = config_lock.MultiProcessConfigManager.get_singleton_instance(
            db_path=constants.CROSS_PROCESS_DATA_BASE_PATH,
            enable_sql_logging=gui_constants.ENABLE_SQL_LOGGING,
            zone=ZoneInfo(common_constants.TIME_ZONE)
        )
        # 主界面优先加载,防止预先使用弹窗导致后续的配置失效
        self.root = ttkb.Window(themename=gui_constants.DEFAULT_THEME,title="配置选择",size=(750,600),resizable=(False, False),alpha=0.98,iconphoto=gui_constants.SMALL_ICON_PATH)
        # gui字体
        ttkb_gui_utils.set_available_unified_font_families(self.root)
        self.ui_font_family= ttkb_gui_utils.first_available_font_family
        #隐藏主窗口
        self.root.withdraw()

        # 存在第二个在配置加载界面的,无所谓,不需要提示用户
        # 如果存在正在使用的进程,需要提示用户是否开启第二个相同程序
        if self.config_manager.check_occupied_config_in_db():
            server_utils.logger_print(msg="occupied config found, prompt user to start new instance", custom_logger=logger)
            if not messagebox.askyesno(title="提示", message="检测到已有程序正在运行，是否重复启动新程序？"):
                server_utils.logger_print(msg="user cancel start new instance", custom_logger=logger)
                sys.exit(0)
        # 如果不存在已有的配置,则直接进入webhook server gui界面
        elif not self.config_manager.check_config_in_db():
            server_utils.logger_print(msg="no configs found, this is first run", custom_logger=logger)
            self.create_config_and_launch_main(first_run=True)
            return
        # 配置数据
        self.invalid_configs: List[Dict] = []
        self.valid_configs: List[Dict] = []
        self.occupied_configs: List[Dict] = []
        # 有效配置ID与路径的映射 方便选择有效配置时加载配置路径进入主界面
        self.selectable_config_paths: Dict[str, str] = {}
        # 上一次导入配置的所在目录:默认是在gui配置目录下
        self.last_import_directory= server_utils.get_real_path_create_dir(path=gui_constants.SERVER_CONFIG_DIR, path_is_dir=True)

        # GUI元素 配置列表表格
        self.config_list_table: Optional[gui_widgets.TreeviewWithFixedGrid] = None
        self.scrollbar:Optional[ttkb.Scrollbar]=None
        self.path_tooltip: Optional[gui_widgets.RowHoverTooltip] = None
        self.status_summary_text = ttkb.StringVar(value=gui_constants.LOADING_CONFIGURATION_MESSAGE)
        
        server_utils.logger_print(msg="creating config selection widgets", custom_logger=logger)
        # 显示主窗口
        self.root.deiconify()
        self.create_widgets()

        # 应用通用GUI设置
        gui_utils.main_gui_after_create_widgets_comm_do(self.root)
        
        # 加载配置数据
        self.refresh_config_list()
        
        # 监听关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        server_utils.logger_print(msg="config selection gui initialization completed", custom_logger=logger)

    def create_widgets(self):
        """创建界面组件"""
        # 主内容区域
        content_frame = ttkb.Frame(self.root)
        content_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)
        
        # 标题
        title_label = ttkb.Label(content_frame, text="配置选择",font=(self.ui_font_family, 16, "bold"))
        title_label.pack(pady=(0, 10))

        # 操作按钮区域
        btn_frame = ttkb.Frame(content_frame)
        btn_frame.pack(fill=X, pady=(0, 10))
        for i in range(6):
            if i == 0 or i == 5:
                btn_frame.grid_columnconfigure(i, weight=1)  # 空列用于撑开空间
            else:
                btn_frame.grid_columnconfigure(i, weight=0)  # 按钮列固定宽度
        same_style="outline-toolbutton"
        ttkb.Button(btn_frame, text="新增配置", command=self.create_new_config,bootstyle=same_style).grid(row=0, column=1, padx=5) # noqa
        ttkb.Button(btn_frame, text="导入配置", command=self.import_config,bootstyle=same_style).grid(row=0, column=2, padx=5) # noqa
        ttkb.Button(btn_frame, text="清理无效", command=self.clear_invalid_configs,bootstyle=same_style).grid(row=0, column=3, padx=5) # noqa
        ttkb.Button(btn_frame, text="刷新列表", command=self.refresh_config_list,bootstyle=same_style).grid(row=0, column=4, padx=5) # noqa

        # 配置列表区域
        list_frame = ttkb.LabelFrame(content_frame, text="配置列表")
        list_frame.pack(fill=BOTH, expand=True, padx=10)
        
        # 创建树视图 - 移除path列，配置路径将通过tooltip显示,style="Custom.Treeview"
        column_dicts=[
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: '#0', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '状态', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 80},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'name', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '配置名称', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 200},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'last_used', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '最后使用', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 120},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'info', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '附加信息', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 250},
        ]
        self.config_list_table = gui_widgets.TreeviewWithFixedGrid(parent=list_frame, font_family=self.ui_font_family, font_size=10, columns= column_dicts, need_tree=True, selectmode="browse")
        self.config_list_table.images.append(gui_utils.create_color_circle_image(color=gui_constants.GREEN_COLOR, row_height=self.config_list_table.row_height))
        self.config_list_table.images.append(gui_utils.create_color_circle_image(color=gui_constants.RED_COLOR, row_height=self.config_list_table.row_height))
        self.config_list_table.images.append(gui_utils.create_red_x_image(self.config_list_table.row_height))

        # 添加滚动条 - 先pack滚动条，确保其位置正确
        self.scrollbar = ttkb.Scrollbar(list_frame, orient="vertical", command=self.config_list_table.yview,bootstyle="round")  # noqa
        self.scrollbar.pack(side=RIGHT, fill=Y)

        # 配置列表表格视图 - 后pack表格，让其适应滚动条的空间
        self.config_list_table.pack(side=LEFT, fill=BOTH, expand=True, padx=10, pady=1)
        self.config_list_table.configure(yscrollcommand=self.scrollbar.set)
        # 绑定双击事件
        gui_utils.bind_double_click_handler(self.config_list_table, self.select_config)
        # 创建行tooltip功能，用于显示配置路径（在双击事件之后绑定）
        self.path_tooltip = gui_widgets.RowHoverTooltip(self.config_list_table, self.ui_font_family, 8)

        # 状态标签
        ttkb.Label(content_frame, width=40, textvariable=self.status_summary_text,font=(self.ui_font_family, 10)).pack(pady=(0, 10))
        
        # 底部按钮
        bottom_frame = ttkb.Frame(self.root)
        bottom_frame.pack(side=BOTTOM, fill=X, padx=20, pady=20)
        
        ttkb.Button(bottom_frame, text="选择配置", command=self.select_config).pack(side=RIGHT, padx=10)

    def refresh_config_list(self):
        """刷新配置列表"""
        server_utils.logger_print(msg="refreshing config list", custom_logger=logger)
        try:
            self.status_summary_text.set(gui_constants.LOADING_CONFIGURATION_MESSAGE)
            # 获取配置状态
            self.invalid_configs, self.valid_configs, self.occupied_configs = self.config_manager.get_latest_config_records()
            # 清空现有数据
            self.config_list_table.delete(*self.config_list_table.get_children())
            # 添加配置到树视图
            self._populate_config_table()
            # 更新状态标签
            self._update_status_summary()
            self.config_list_table.yview_moveto(0)
            server_utils.logger_print(msg="config list refreshed successfully", custom_logger=logger)
        except Exception as e:
            server_utils.logger_print(msg="error refreshing config list!", custom_logger=logger, use_exception=True, exception=e)
            messagebox.showerror("刷新配置列表失败", str(e),parent=self.root)

    def create_config_and_launch_main(self,first_run:bool):
        """创建新配置并进入主界面"""
        msg= "handling first run scenario" if first_run else "creating new config"
        msg+="  to main gui"
        error_msg = "error in first run handling!" if first_run else "error creating new config!"
        error_zh_msg = "首次运行初始化失败！" if first_run else "创建新配置失败！"
        server_utils.logger_print(msg=msg, custom_logger=logger)

        try:
            config_path = config_check.create_new_config_file()
            server_utils.logger_print(msg=f"created new config file: {config_path}", custom_logger=logger)

            # 新增配置:在server_gui进行使用该配置
            self.config_manager.load_import_config(config_path)
            server_utils.logger_print(msg=f"registered config: {config_path}", custom_logger=logger)

            # 直接进入主界面
            self.launch_main_gui(config_path)
        except Exception as e:
            server_utils.logger_print(msg=error_msg, custom_logger=logger, use_exception=True, exception=e)
            messagebox.showerror("加载主界面错误", f"{error_zh_msg}：{str(e)}",parent=self.root)

    def _populate_config_table(self):
        """将配置添加到树视图"""
        server_utils.logger_print(msg="adding configs to tree view", custom_logger=logger)
        self.selectable_config_paths.clear()
        self.path_tooltip.clear_all_row_tooltips()
        # 滚动条在数据更新之后判断是否需要显示
        # 先pack_forget再重新pack以保证顺序 --- pack_forget一定是需要执行的
        self.scrollbar.pack_forget()
        if (len(self.valid_configs)+len(self.occupied_configs)+len(self.invalid_configs))>gui_constants.CONFIG_PAGE_NUM:
            # 确保滚动条在正确位置显示
            self.scrollbar.pack(side=RIGHT, fill=Y)
        # 添加可用配置
        for config in self.valid_configs:
            cur_file_path = config['file_path']
            tooltip_text = f"{gui_constants.ROW_TIP_PREFIX}{repr(cur_file_path)[1:-1]}"
            def valid_row_processor(treeview, item_id, row_data, index, tag): # noqa
                """处理可用配置行"""
                self.selectable_config_paths[item_id] = cur_file_path
                # 为该行设置tooltip显示配置路径
                self.path_tooltip.set_row_tooltip(item_id, tooltip_text)
                treeview.item(item_id, tags=(tag, gui_constants.VALID_TAG,), image=treeview.images[0])

            new_valid_data=[{'#0':'可用','name':config['name'],'last_used': server_utils.format_time_for_display(config['last_used'])}]
            self.config_list_table.add_rows(new_valid_data, valid_row_processor,need_update_separators=False)

        # 添加占用配置
        for config in self.occupied_configs:
            process_info = f"对应进程PID: {config['process_id']}"
            tooltip_text = f"{gui_constants.ROW_TIP_PREFIX}{repr(config['file_path'])[1:-1]}"
            def occupied_row_processor(treeview, item_id, row_data, index, tag): # noqa
                """处理占用配置行"""
                # 为该行设置tooltip显示配置路径
                self.path_tooltip.set_row_tooltip(item_id, tooltip_text)
                treeview.item(item_id, tags=(tag, gui_constants.OCCUPIED_TAG,), image=treeview.images[1])

            occupied_data=[{'#0':'占用','name':config['name'],'last_used': server_utils.format_time_for_display(config['last_used']), 'info':process_info}]
            self.config_list_table.add_rows(occupied_data, occupied_row_processor,need_update_separators=False)

        # 添加无效配置
        for config in self.invalid_configs:
            fail_reason = f"配置失效原因: {config['fail_reason']}"
            tooltip_text = f"{gui_constants.ROW_TIP_PREFIX}{repr(config['file_path'])[1:-1]}"
            def invalid_row_processor(treeview, item_id, row_data, index, tag): # noqa
                """处理无效配置行"""
                # 为该行设置tooltip显示配置路径
                self.path_tooltip.set_row_tooltip(item_id, tooltip_text)
                treeview.item(item_id, tags=(tag, gui_constants.INVALID_TAG,), image=treeview.images[2])

            invalid_data=[{'#0':'无效','name':config['name'],'last_used': server_utils.format_time_for_display(config['last_used']), 'info':fail_reason}]
            self.config_list_table.add_rows(invalid_data, invalid_row_processor,need_update_separators=False)

        # 统一在数据插入完毕后更新分隔符
        self.config_list_table.update_separators()
        server_utils.logger_print(msg="configs added to tree view successfully", custom_logger=logger)

    def _update_status_summary(self):
        """更新状态标签"""
        valid_count = len(self.valid_configs)
        occupied_count = len(self.occupied_configs)
        invalid_count = len(self.invalid_configs)
        status_text = f"可用配置: {valid_count}个  占用配置: {occupied_count}个  无效配置: {invalid_count}个"
        self.status_summary_text.set(status_text)

    def create_new_config(self):
        """创建新配置:按钮事件"""
        self.create_config_and_launch_main(first_run=False)

    def import_config(self):
        """导入配置文件"""
        server_utils.logger_print(msg="importing config file", custom_logger=logger)

        try:
            # 选择配置文件
            file_path = filedialog.askopenfilename(
                title="选择配置文件",
                filetypes=[("INI files", "*.ini")],
                initialdir=self.last_import_directory
            )
            
            if not file_path:
                return
            server_utils.logger_print(msg=f"selected config file: {file_path}", custom_logger=logger)
            can_register, fail_reason = self.config_manager.check_config_registrable(file_path)
            if not can_register:
                messagebox.showerror("该配置文件不可用", fail_reason,parent=self.root)
                self.refresh_config_list()
                return

            server_utils.logger_print(msg=f"registered imported config: {file_path}", custom_logger=logger)
            self.last_import_directory=os.path.dirname(file_path)
            # 配置文件的使用是webhook_server_gui的事情,和当前配置加载界面无关
            self.config_manager.load_import_config(file_path)
            if messagebox.askyesno("加载到主界面","是否使用导入的配置直接进入主界面?",parent=self.root):
                # 进入主界面
                self.launch_main_gui(file_path)
            else:
                # 刷新配置列表
                self.refresh_config_list()
            
        except Exception as e:
            server_utils.logger_print(msg="error importing config!", custom_logger=logger, use_exception=True, exception=e)
            messagebox.showerror("导入配置失败", str(e),parent=self.root)

    def clear_invalid_configs(self):
        """清理无效配置:按钮事件"""
        server_utils.logger_print(msg="clearing invalid configs", custom_logger=logger)
        try:
            had_deleted_count=self.config_manager.delete_invalid_configs()
            server_utils.logger_print(msg="invalid configs cleared successfully", custom_logger=logger)
            if had_deleted_count>0:
                messagebox.showinfo("清除成功", f"成功删除无效配置{had_deleted_count}个",parent=self.root)
            else:
                messagebox.showwarning(message="无效配置不存在",parent=self.root)
        except Exception as e:
            server_utils.logger_print(msg="error clearing invalid configs!", custom_logger=logger, use_exception=True, exception=e)
            messagebox.showerror("清理无效配置失败", str(e),parent=self.root)
        finally:
            self.refresh_config_list()


    def select_config(self):
        """选择配置进入主界面:按钮或者双击事件"""
        selected = self.config_list_table.selection()
        if not selected:
            messagebox.showwarning(message="请先选择一个配置",parent=self.root)
            return
        if len(selected)>1:
            messagebox.showwarning(message="只能选择一个配置进行操作",parent=self.root)
            return

        # 获取选中的配置信息
        item_id = selected[0]
        item = self.config_list_table.item(item_id)
        values = item['values']
        tags=item['tags']

        if not values:
            return

        # 检查配置状态
        if gui_constants.OCCUPIED_TAG in tags:
            messagebox.showwarning(message="该配置正被其他进程占用，无法加载使用",parent=self.root)
            return
        if gui_constants.INVALID_TAG in tags:
            messagebox.showwarning(message="该配置无效，无法使用",parent=self.root)
            return
        if item_id not in self.selectable_config_paths.keys():
            messagebox.showwarning(message="该配置已被删除，无法使用",parent=self.root)
            return

        # 从配置数据中获取路径信息，而不是从表格列中获取
        config_path = self.selectable_config_paths[item_id]
        if not config_path:
            messagebox.showerror("配置加载失败", "无法获取配置路径信息",parent=self.root)
            return

        server_utils.logger_print(msg=f"selecting config: {config_path}", custom_logger=logger)
        # 进入主界面
        self.launch_main_gui(config_path)

    def launch_main_gui(self, config_path: str):
        """根据配置文件在新进程中加载主界面"""
        server_utils.logger_print(msg=f"launching main gui in new process with config: {config_path}", custom_logger=logger)
        try:
            # 隐藏当前配置选择界面
            if hasattr(self, "root") and self.root and hasattr(self, "path_tooltip") and self.path_tooltip:
                self.path_tooltip.hide_tooltip()
                self.root.withdraw()
            ConfigSelectionGUI.error_had_shown=False
            # 启动新进程 - 修复：使用config_selection_gui.py脚本并传递--main-gui参数
            main_gui_process = webhook_server_utils.start_independent_process(config_path=config_path, argument_param='--main-gui', relative_script_path='./src/webhook_server/config_selection_gui.py')
            server_utils.logger_print(msg=f"main gui process started with pid: {main_gui_process.pid}", custom_logger=logger)
            # 等待主界面启动成功的延迟检测
            self._monitor_main_gui_startup(main_gui_process)
        except Exception as e:
            server_utils.logger_print(msg="error launching main gui in new process!", custom_logger=logger, use_exception=True, exception=e)
            messagebox.showerror("启动主界面失败", f"在新进程中启动主界面时发生错误：{str(e)}")
            self._handle_main_gui_launch_error(f"在新进程中启动主界面时发生错误：{str(e)}")

    def _monitor_main_gui_startup(self, main_gui_process):
        """等待主界面启动成功，然后关闭当前配置选择界面"""
        exit_code= server_utils.monitor_independent_process_startup(main_gui_process)
        if exit_code is not None and exit_code!= 0:
            # 主界面启动失败，尝试读取详细错误信息
            error_message = webhook_server_utils.read_error_file_by_pid(main_gui_process.pid)
            if error_message:
                self._handle_main_gui_launch_error(f"主界面启动失败：\n\n{error_message}")
            else:
                self._handle_main_gui_launch_error(f"主界面进程异常退出，退出代码：{exit_code}\n\n"
                                                   f"可能的原因：\n"
                                                   f"1. 配置文件有问题\n"
                                                   f"2. 依赖库缺失\n"
                                                   f"3. 权限不足\n\n"
                                                   f"请检查日志文件获取详细错误信息。")
            return

        # 启动成功，关闭当前配置选择界面
        server_utils.logger_print(msg="main gui startup delay completed, closing config selection gui", custom_logger=logger)
        # 关闭当前配置选择界面
        self.on_closing()

    # 对于错误是否已经处理过的标记
    error_had_shown = False
    def _handle_main_gui_launch_error(self, error_msg: str):
        """运行主界面失败时的错误处理"""
        if ConfigSelectionGUI.error_had_shown:
            return

        ConfigSelectionGUI.error_had_shown = True
        messagebox.showerror("启动主界面失败", error_msg)
        # 显示当前配置选择界面
        self.root.deiconify()
        self.refresh_config_list()

    def on_closing(self):
        """关闭事件处理"""
        server_utils.logger_print(msg="config selection gui closing", custom_logger=logger)
        if hasattr(self, "root"):
            gui_utils.gui_close(self.root)


def main():
    """主函数，处理命令行参数"""
    import argparse

    parser = argparse.ArgumentParser(description="Webhook Server Configuration GUI")
    parser.add_argument("--main-gui", help="直接启动主界面，传入配置文件路径")
    parser.add_argument("--webhook", help="创建webhook server，传入webhook server配置路径")
    args = parser.parse_args()

    if args.main_gui:
        # 直接启动主界面模式
        try:
            server_utils.logger_print(msg=f"launching main gui directly with config: {args.main_gui}", custom_logger=logger)
            main_app = webhook_server_gui.WebhookServerGUI(args.main_gui)
            main_app.root.mainloop()
        except Exception as e:
            server_utils.logger_print(msg="error launching main gui directly!", custom_logger=logger, use_exception=True, exception=e)
            webhook_server_utils.write_error_to_temp_file(str(e))
            sys.exit(1)
    elif args.webhook:
        try:
            server_config_path = args.webhook
            server_utils.logger_print(msg=f"create webhook server: {server_config_path}", custom_logger=logger)
            asyncio.run(webhook_server.run_server_with_path(config_path=server_config_path,is_child_process=True))
        except BaseException as e:
            server_utils.logger_print(msg="error creating webhook server!", custom_logger=logger, use_exception=True, exception=e)
            sys.exit(1)
    else:
        # 正常启动配置选择界面
        try:
            app = ConfigSelectionGUI()
            # 首次运行时 'root' 是不存在的
            if hasattr(app, "root"):
                app.root.mainloop()
        except Exception as unknown_exp:
            server_utils.logger_print(msg="unknown error occurred!", custom_logger=logger, use_exception=True, exception=unknown_exp)
            messagebox.showerror("程序崩溃", f"发生未预期错误: {type(unknown_exp).__name__}\n\n请将日志文件发送给开发者处理")


if __name__ == "__main__":
    main()
