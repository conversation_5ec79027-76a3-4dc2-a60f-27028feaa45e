#!/usr/bin/env python3
"""测试GUI启动的脚本"""

import sys
import traceback
from pathlib import Path

# 设置Python路径
src_path = str(Path(__file__).parent)
if src_path not in sys.path:
    sys.path.insert(0, src_path)

try:
    print("开始导入模块...")
    from src.webhook_server_gui import WebhookServerGUI
    print("模块导入成功")
    
    print("开始创建GUI实例...")
    config_path = "tests/other_conf/ok/server_config_3.ini"
    print(f"使用配置文件: {config_path}")
    
    app = WebhookServerGUI(config_path)
    print("GUI实例创建成功")
    
    print("开始运行主循环...")
    app.root.mainloop()
    
except Exception as e:
    print(f"错误类型: {type(e).__name__}")
    print(f"错误信息: {str(e)}")
    print("完整错误堆栈:")
    traceback.print_exc()
    sys.exit(1)
